# TonLiker - NFT Automation Tool for getgems.io

A C# .NET Core 9 application that automates NFT "liking" on getgems.io using Selenium WebDriver with Chrome. Supports multiple concurrent browser sessions (channels) with isolated profiles and comprehensive security features.

## Features

### 🚀 Core Functionality
- **Multi-Session Support**: Run multiple independent browser instances simultaneously
- **Session Persistence**: Cookies and cache stored per channel, persist across restarts
- **Domain Enforcement**: Strict validation to ensure actions only occur on getgems.io
- **NFT Like Automation**: Automatically scroll and like NFTs while avoiding duplicates
- **File Import**: Import NFT URLs from CSV or TXT files with validation

### 🔒 Security Features
- **Domain Validation**: Prevents actions on unauthorized domains
- **URL Validation**: Validates NFT URLs before processing
- **Suspicious Pattern Detection**: Blocks potentially malicious URLs
- **Cross-Domain Protection**: Detects and prevents unintended redirects

### ⚙️ Configuration
- **Flexible Settings**: JSON-based configuration with validation
- **Channel Management**: Configure multiple channels with individual settings
- **Chrome Customization**: Custom Chrome arguments and profile paths
- **Logging**: Comprehensive logging with per-channel log files

## Quick Start

### Prerequisites
- .NET Core 9.0 SDK
- Google Chrome browser
- Windows, macOS, or Linux

### Installation

1. Clone or download the project
2. Navigate to the TonLiker directory
3. Restore dependencies:
   ```bash
   dotnet restore
   ```
4. Build the project:
   ```bash
   dotnet build
   ```

### Basic Usage

1. **Process NFTs from a file:**
   ```bash
   dotnet run -- file ./InputFiles/nfts.csv
   ```

2. **Process NFTs from a specific page:**
   ```bash
   dotnet run -- page "https://getgems.io/collection/example"
   ```

3. **Validate URLs in a file:**
   ```bash
   dotnet run -- validate ./InputFiles/urls.txt
   ```

4. **Show configuration:**
   ```bash
   dotnet run -- config show
   ```

## Configuration

The application uses `appsettings.json` for configuration. A default configuration is created automatically on first run.

### Key Configuration Sections

#### Basic Settings
```json
{
  "SessionDataPath": "./SessionData",
  "LogsPath": "./Logs",
  "InputFilesPath": "./InputFiles",
  "MaxConcurrentChannels": 3,
  "GlobalDelayBetweenChannels": 5000,
  "EnableHeadlessMode": false,
  "EnableDomainValidation": true,
  "AllowedDomains": ["getgems.io"]
}
```

#### Channel Configuration
```json
{
  "Channels": [
    {
      "Name": "Channel1",
      "ProfilePath": "",
      "IsEnabled": true,
      "DelayBetweenActions": 1000,
      "ScrollDelay": 2000,
      "MaxRetries": 3,
      "TonWalletAddress": null
    }
  ]
}
```

#### Chrome Settings
```json
{
  "Chrome": {
    "PageLoadTimeout": 30,
    "ImplicitWaitTimeout": 10,
    "DefaultArguments": [
      "--disable-blink-features=AutomationControlled",
      "--disable-extensions",
      "--no-sandbox"
    ]
  }
}
```

#### Security Settings
```json
{
  "Security": {
    "ValidateNftUrls": true,
    "PreventCrossDomainActions": true,
    "MaxUrlValidationRetries": 3,
    "UrlValidationTimeout": 10,
    "SuspiciousPatterns": ["phishing", "scam", "fake"]
  }
}
```

## File Formats

### CSV Format
The application supports CSV files with the following structure:

```csv
Url,Title,Collection
https://getgems.io/nft/example1,NFT Title 1,Collection Name
https://getgems.io/nft/example2,NFT Title 2,Collection Name
```

### TXT Format
Simple text files with one URL per line:

```
https://getgems.io/nft/example1
https://getgems.io/nft/example2
https://getgems.io/nft/example3
```

## Directory Structure

```
TonLiker/
├── SessionData/          # Browser profiles and session data
│   └── Profiles/
│       ├── Channel1/
│       └── Channel2/
├── Logs/                 # Application logs
│   ├── channels/         # Per-channel logs
│   ├── tonliker-*.log    # Main application logs
│   └── errors-*.log      # Error-only logs
├── InputFiles/           # Input CSV/TXT files
└── appsettings.json      # Configuration file
```

## Advanced Usage

### Multiple Channels
Configure multiple channels for concurrent processing:

```json
{
  "Channels": [
    {
      "Name": "Channel1",
      "IsEnabled": true,
      "DelayBetweenActions": 1000,
      "TonWalletAddress": "wallet1_address"
    },
    {
      "Name": "Channel2", 
      "IsEnabled": true,
      "DelayBetweenActions": 1500,
      "TonWalletAddress": "wallet2_address"
    }
  ]
}
```

### Custom Chrome Profiles
Specify custom profile paths for each channel:

```json
{
  "Name": "Channel1",
  "ProfilePath": "C:/CustomProfiles/Channel1",
  "IsEnabled": true
}
```

### Headless Mode
Enable headless mode for server environments:

```json
{
  "EnableHeadlessMode": true
}
```

## Logging

The application provides comprehensive logging:

- **Main logs**: `./Logs/tonliker-YYYY-MM-DD.log`
- **Error logs**: `./Logs/errors-YYYY-MM-DD.log`
- **Channel logs**: `./Logs/channels/channel-{ChannelName}-YYYY-MM-DD.log`
- **Performance logs**: `./Logs/performance-YYYY-MM-DD.log`

## Security Considerations

1. **Domain Validation**: Always keep domain validation enabled
2. **URL Validation**: Validate all URLs before processing
3. **Profile Isolation**: Each channel uses isolated browser profiles
4. **Suspicious Pattern Detection**: Monitor for phishing attempts
5. **Rate Limiting**: Configure appropriate delays between actions

## Troubleshooting

### Common Issues

1. **Chrome Driver Issues**
   - Ensure Chrome browser is installed
   - Check Chrome version compatibility
   - Verify ChromeDriver is accessible

2. **Permission Errors**
   - Run with appropriate permissions
   - Check directory access rights
   - Verify profile path permissions

3. **Network Issues**
   - Check internet connectivity
   - Verify getgems.io accessibility
   - Review firewall settings

4. **Configuration Errors**
   - Validate JSON syntax
   - Check required fields
   - Review channel configurations

### Debug Mode
Enable detailed logging by modifying the log level in the configuration.

## Performance Tips

1. **Optimize Delays**: Adjust delays based on your network speed
2. **Limit Concurrent Channels**: Start with 2-3 channels and increase gradually
3. **Use Headless Mode**: Reduces resource usage on servers
4. **Monitor Logs**: Watch for errors and performance issues
5. **Regular Cleanup**: Clean old session data and logs periodically

## Legal and Ethical Considerations

- Ensure compliance with getgems.io terms of service
- Respect rate limits and server resources
- Use responsibly and ethically
- Consider the impact on the platform and other users

## Support

For issues, questions, or contributions:
1. Check the logs for detailed error information
2. Review the configuration settings
3. Ensure all prerequisites are met
4. Test with a small dataset first

## License

This project is provided as-is for educational and automation purposes. Users are responsible for compliance with applicable terms of service and regulations.
