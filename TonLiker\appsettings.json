{"SessionDataPath": "./SessionData", "LogsPath": "./Logs", "InputFilesPath": "./InputFiles", "MaxConcurrentChannels": 3, "GlobalDelayBetweenChannels": 5000, "EnableHeadlessMode": false, "EnableDomainValidation": true, "AllowedDomains": ["getgems.io"], "Channels": [{"Name": "Channel1", "ProfilePath": "", "IsEnabled": true, "DelayBetweenActions": 1000, "ScrollDelay": 2000, "MaxRetries": 3, "TonWalletAddress": null, "CustomHeaders": {}, "UserAgents": []}], "Chrome": {"ChromeDriverPath": null, "ChromeBinaryPath": null, "PageLoadTimeout": 30, "ImplicitWaitTimeout": 10, "DefaultArguments": ["--disable-blink-features=AutomationControlled", "--disable-extensions", "--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--remote-debugging-port=0"]}, "Security": {"ValidateNftUrls": true, "PreventCrossDomainActions": true, "MaxUrlValidationRetries": 3, "UrlValidationTimeout": 10, "BlockedDomains": [], "SuspiciousPatterns": ["phishing", "scam", "fake", "malicious"]}}