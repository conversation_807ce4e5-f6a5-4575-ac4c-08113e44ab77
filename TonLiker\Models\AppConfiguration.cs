namespace TonLiker.Models;

/// <summary>
/// Main application configuration
/// </summary>
public class AppConfiguration
{
    public string SessionDataPath { get; set; } = "./SessionData";
    public string LogsPath { get; set; } = "./Logs";
    public string InputFilesPath { get; set; } = "./InputFiles";
    public int MaxConcurrentChannels { get; set; } = 3;
    public int GlobalDelayBetweenChannels { get; set; } = 5000; // milliseconds
    public bool EnableHeadlessMode { get; set; } = false;
    public bool EnableDomainValidation { get; set; } = true;
    public List<string> AllowedDomains { get; set; } = new() { "getgems.io" };
    public List<ChannelConfiguration> Channels { get; set; } = new();
    public ChromeConfiguration Chrome { get; set; } = new();
    public SecurityConfiguration Security { get; set; } = new();
}

/// <summary>
/// Chrome-specific configuration
/// </summary>
public class ChromeConfiguration
{
    public string? ChromeDriverPath { get; set; }
    public string? ChromeBinaryPath { get; set; }
    public int PageLoadTimeout { get; set; } = 30; // seconds
    public int ImplicitWaitTimeout { get; set; } = 10; // seconds
    public List<string> DefaultArguments { get; set; } = new()
    {
        "--disable-blink-features=AutomationControlled",
        "--disable-extensions",
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--remote-debugging-port=0"
    };
}

/// <summary>
/// Security and validation configuration
/// </summary>
public class SecurityConfiguration
{
    public bool ValidateNftUrls { get; set; } = true;
    public bool PreventCrossDomainActions { get; set; } = true;
    public int MaxUrlValidationRetries { get; set; } = 3;
    public int UrlValidationTimeout { get; set; } = 10; // seconds
    public List<string> BlockedDomains { get; set; } = new();
    public List<string> SuspiciousPatterns { get; set; } = new()
    {
        "phishing",
        "scam",
        "fake",
        "malicious"
    };
}
