using OpenQA.Selenium;
using Serilog;

namespace TonLiker.Models;

/// <summary>
/// Represents an active browser session for a channel
/// </summary>
public class ChannelSession : IDisposable
{
    public string ChannelName { get; }
    public ChannelConfiguration Configuration { get; }
    public IWebDriver Driver { get; }
    public ILogger Logger { get; }
    public DateTime StartTime { get; }
    public bool IsActive { get; private set; } = true;
    public int ProcessedNfts { get; private set; } = 0;
    public int SuccessfulLikes { get; private set; } = 0;
    public int FailedAttempts { get; private set; } = 0;

    public ChannelSession(string channelName, ChannelConfiguration configuration, IWebDriver driver, ILogger logger)
    {
        ChannelName = channelName;
        Configuration = configuration;
        Driver = driver;
        Logger = logger;
        StartTime = DateTime.UtcNow;
    }

    public void IncrementProcessed() => ProcessedNfts++;
    public void IncrementSuccessful() => SuccessfulLikes++;
    public void IncrementFailed() => FailedAttempts++;

    public void Stop()
    {
        IsActive = false;
        Logger.Information("Channel {ChannelName} stopped. Processed: {Processed}, Successful: {Successful}, Failed: {Failed}",
            ChannelName, ProcessedNfts, SuccessfulLikes, FailedAttempts);
    }

    public void Dispose()
    {
        try
        {
            Stop();
            Driver?.Quit();
            Driver?.Dispose();
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error disposing channel session {ChannelName}", ChannelName);
        }
    }
}
