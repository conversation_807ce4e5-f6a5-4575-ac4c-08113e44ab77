# TonLiker Usage Examples

This document provides practical examples of how to use <PERSON>n<PERSON><PERSON><PERSON> for various scenarios.

## Basic Examples

### 1. Process NFTs from a CSV file
```bash
# Process all NFTs listed in a CSV file
dotnet run -- file ./InputFiles/my_nfts.csv

# Process sample NFTs (included with the project)
dotnet run -- file ./InputFiles/sample_nfts.csv
```

### 2. Process NFTs from a TXT file
```bash
# Process URLs from a text file
dotnet run -- file ./InputFiles/nft_urls.txt

# Process sample URLs (included with the project)
dotnet run -- file ./InputFiles/sample_urls.txt
```

### 3. Process a specific getgems.io page
```bash
# Process all NFTs on a collection page
dotnet run -- page "https://getgems.io/collection/example-collection"

# Process NFTs from a specific user's profile
dotnet run -- page "https://getgems.io/user/example-user"
```

### 4. Validate URLs before processing
```bash
# Validate URLs in a file without processing them
dotnet run -- validate ./InputFiles/my_nfts.csv
dotnet run -- validate ./InputFiles/urls_to_check.txt
```

## Configuration Examples

### 1. View current configuration
```bash
dotnet run -- config show
```

### 2. Create example configuration file
```bash
dotnet run -- config create
```

## Advanced Configuration Scenarios

### Scenario 1: High-Performance Setup (3 Channels)
```json
{
  "MaxConcurrentChannels": 3,
  "GlobalDelayBetweenChannels": 3000,
  "EnableHeadlessMode": false,
  "Channels": [
    {
      "Name": "FastChannel",
      "IsEnabled": true,
      "DelayBetweenActions": 800,
      "ScrollDelay": 1500,
      "MaxRetries": 2
    },
    {
      "Name": "MediumChannel", 
      "IsEnabled": true,
      "DelayBetweenActions": 1200,
      "ScrollDelay": 2000,
      "MaxRetries": 3
    },
    {
      "Name": "SlowChannel",
      "IsEnabled": true,
      "DelayBetweenActions": 2000,
      "ScrollDelay": 3000,
      "MaxRetries": 5
    }
  ]
}
```

### Scenario 2: Conservative Setup (1 Channel, Slow)
```json
{
  "MaxConcurrentChannels": 1,
  "GlobalDelayBetweenChannels": 10000,
  "EnableHeadlessMode": false,
  "Channels": [
    {
      "Name": "ConservativeChannel",
      "IsEnabled": true,
      "DelayBetweenActions": 3000,
      "ScrollDelay": 5000,
      "MaxRetries": 5
    }
  ]
}
```

### Scenario 3: Server/Headless Setup
```json
{
  "MaxConcurrentChannels": 2,
  "EnableHeadlessMode": true,
  "Chrome": {
    "DefaultArguments": [
      "--headless=new",
      "--disable-blink-features=AutomationControlled",
      "--disable-extensions",
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-gpu",
      "--remote-debugging-port=0",
      "--window-size=1920,1080"
    ]
  }
}
```

## File Format Examples

### CSV File with Full Information
```csv
Url,Title,Collection
https://getgems.io/nft/EQD-example1,Cool NFT #1,Awesome Collection
https://getgems.io/nft/EQD-example2,Cool NFT #2,Awesome Collection
https://getgems.io/nft/EQD-example3,Rare NFT #1,Rare Collection
```

### CSV File with URLs Only
```csv
Url
https://getgems.io/nft/EQD-example1
https://getgems.io/nft/EQD-example2
https://getgems.io/nft/EQD-example3
```

### TXT File with Comments
```txt
# My NFT Collection - Batch 1
# Process these first

https://getgems.io/nft/EQD-example1
https://getgems.io/nft/EQD-example2

# High priority NFTs
https://getgems.io/nft/EQD-priority1
https://getgems.io/nft/EQD-priority2
```

## Workflow Examples

### Workflow 1: Safe Testing
1. Start with validation:
   ```bash
   dotnet run -- validate ./InputFiles/test_nfts.csv
   ```

2. Process a small batch:
   ```bash
   # Create a small test file with 3-5 URLs
   dotnet run -- file ./InputFiles/small_test.csv
   ```

3. Review logs and results before scaling up

### Workflow 2: Large Batch Processing
1. Prepare your file with all NFT URLs
2. Validate the entire file:
   ```bash
   dotnet run -- validate ./InputFiles/large_batch.csv
   ```

3. Configure multiple channels for faster processing
4. Process in chunks if needed:
   ```bash
   # Split large files into smaller chunks
   dotnet run -- file ./InputFiles/batch_1.csv
   dotnet run -- file ./InputFiles/batch_2.csv
   ```

### Workflow 3: Continuous Monitoring
1. Set up logging directory monitoring
2. Process files as they arrive:
   ```bash
   # Process new files in a loop
   for file in ./InputFiles/new_*.csv; do
     dotnet run -- file "$file"
     mv "$file" "./InputFiles/processed/"
   done
   ```

## Troubleshooting Examples

### Check if Chrome is working
```bash
# Test with a simple page first
dotnet run -- page "https://getgems.io"
```

### Debug configuration issues
```bash
# Show current config
dotnet run -- config show

# Create fresh config
dotnet run -- config create
```

### Monitor logs in real-time
```bash
# Windows
Get-Content ./Logs/tonliker-*.log -Wait

# Linux/Mac
tail -f ./Logs/tonliker-*.log
```

## Performance Optimization Examples

### For Fast Networks
```json
{
  "DelayBetweenActions": 500,
  "ScrollDelay": 1000,
  "Chrome": {
    "PageLoadTimeout": 15,
    "ImplicitWaitTimeout": 5
  }
}
```

### For Slow Networks
```json
{
  "DelayBetweenActions": 3000,
  "ScrollDelay": 5000,
  "Chrome": {
    "PageLoadTimeout": 60,
    "ImplicitWaitTimeout": 15
  }
}
```

### For Maximum Stealth
```json
{
  "DelayBetweenActions": 2000,
  "ScrollDelay": 4000,
  "GlobalDelayBetweenChannels": 10000,
  "MaxConcurrentChannels": 1,
  "Chrome": {
    "DefaultArguments": [
      "--disable-blink-features=AutomationControlled",
      "--disable-extensions",
      "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    ]
  }
}
```

## Security Examples

### Maximum Security Configuration
```json
{
  "EnableDomainValidation": true,
  "AllowedDomains": ["getgems.io"],
  "Security": {
    "ValidateNftUrls": true,
    "PreventCrossDomainActions": true,
    "MaxUrlValidationRetries": 3,
    "UrlValidationTimeout": 10,
    "BlockedDomains": ["suspicious-site.com"],
    "SuspiciousPatterns": [
      "phishing", "scam", "fake", "malicious", 
      "free-nft", "claim-now", "urgent"
    ]
  }
}
```

### Testing Security Features
```bash
# Test with invalid URLs to verify security
echo "https://malicious-site.com/fake-nft" > test_security.txt
dotnet run -- validate test_security.txt
# Should show validation errors
```

## Integration Examples

### Batch Script (Windows)
```batch
@echo off
echo Starting TonLiker batch processing...

for %%f in (.\InputFiles\batch_*.csv) do (
    echo Processing %%f
    dotnet run -- file "%%f"
    if errorlevel 1 (
        echo Error processing %%f
        pause
    )
)

echo Batch processing completed.
pause
```

### Shell Script (Linux/Mac)
```bash
#!/bin/bash
echo "Starting TonLiker batch processing..."

for file in ./InputFiles/batch_*.csv; do
    if [ -f "$file" ]; then
        echo "Processing $file"
        dotnet run -- file "$file"
        
        if [ $? -eq 0 ]; then
            echo "Successfully processed $file"
            mv "$file" "./InputFiles/completed/"
        else
            echo "Error processing $file"
            mv "$file" "./InputFiles/failed/"
        fi
    fi
done

echo "Batch processing completed."
```

These examples should help you get started with TonLiker and adapt it to your specific needs. Remember to always test with small batches first and monitor the logs for any issues.
