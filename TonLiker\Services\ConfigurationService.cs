using Microsoft.Extensions.Configuration;
using Serilog;
using System.Text.Json;
using TonLiker.Models;

namespace TonLiker.Services;

/// <summary>
/// Manages application configuration loading and validation
/// </summary>
public class ConfigurationService
{
    private readonly ILogger _logger;
    private AppConfiguration? _configuration;

    public ConfigurationService(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Loads configuration from appsettings.json and validates it
    /// </summary>
    public AppConfiguration LoadConfiguration(string? configPath = null)
    {
        try
        {
            var configFile = configPath ?? "appsettings.json";
            
            if (!File.Exists(configFile))
            {
                _logger.Warning("Configuration file {ConfigFile} not found, creating default configuration", configFile);
                CreateDefaultConfiguration(configFile);
            }

            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile(configFile, optional: false, reloadOnChange: true);

            var config = builder.Build();
            var appConfig = new AppConfiguration();
            config.Bind(appConfig);

            ValidateConfiguration(appConfig);
            EnsureDirectoriesExist(appConfig);
            
            _configuration = appConfig;
            
            _logger.Information("Configuration loaded successfully from {ConfigFile}", configFile);
            return appConfig;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error loading configuration");
            throw new InvalidOperationException($"Failed to load configuration: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Saves configuration to file
    /// </summary>
    public async Task SaveConfigurationAsync(AppConfiguration config, string? configPath = null)
    {
        try
        {
            var configFile = configPath ?? "appsettings.json";
            
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var json = JsonSerializer.Serialize(config, options);
            await File.WriteAllTextAsync(configFile, json);
            
            _logger.Information("Configuration saved to {ConfigFile}", configFile);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error saving configuration to {ConfigFile}", configPath);
            throw;
        }
    }

    /// <summary>
    /// Creates a default configuration file
    /// </summary>
    private void CreateDefaultConfiguration(string configFile)
    {
        var defaultConfig = new AppConfiguration
        {
            SessionDataPath = "./SessionData",
            LogsPath = "./Logs",
            InputFilesPath = "./InputFiles",
            MaxConcurrentChannels = 3,
            GlobalDelayBetweenChannels = 5000,
            EnableHeadlessMode = false,
            EnableDomainValidation = true,
            AllowedDomains = new List<string> { "getgems.io" },
            Channels = new List<ChannelConfiguration>
            {
                new()
                {
                    Name = "Channel1",
                    ProfilePath = "",
                    IsEnabled = true,
                    DelayBetweenActions = 1000,
                    ScrollDelay = 2000,
                    MaxRetries = 3
                }
            },
            Chrome = new ChromeConfiguration
            {
                PageLoadTimeout = 30,
                ImplicitWaitTimeout = 10,
                DefaultArguments = new List<string>
                {
                    "--disable-blink-features=AutomationControlled",
                    "--disable-extensions",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--remote-debugging-port=0"
                }
            },
            Security = new SecurityConfiguration
            {
                ValidateNftUrls = true,
                PreventCrossDomainActions = true,
                MaxUrlValidationRetries = 3,
                UrlValidationTimeout = 10,
                BlockedDomains = new List<string>(),
                SuspiciousPatterns = new List<string> { "phishing", "scam", "fake", "malicious" }
            }
        };

        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        var json = JsonSerializer.Serialize(defaultConfig, options);
        File.WriteAllText(configFile, json);
    }

    /// <summary>
    /// Validates the configuration for required fields and logical consistency
    /// </summary>
    private void ValidateConfiguration(AppConfiguration config)
    {
        var errors = new List<string>();

        // Validate basic settings
        if (config.MaxConcurrentChannels <= 0)
            errors.Add("MaxConcurrentChannels must be greater than 0");

        if (config.GlobalDelayBetweenChannels < 0)
            errors.Add("GlobalDelayBetweenChannels cannot be negative");

        // Validate paths
        if (string.IsNullOrWhiteSpace(config.SessionDataPath))
            errors.Add("SessionDataPath cannot be empty");

        if (string.IsNullOrWhiteSpace(config.LogsPath))
            errors.Add("LogsPath cannot be empty");

        if (string.IsNullOrWhiteSpace(config.InputFilesPath))
            errors.Add("InputFilesPath cannot be empty");

        // Validate channels
        if (!config.Channels.Any())
            errors.Add("At least one channel must be configured");

        if (!config.Channels.Any(c => c.IsEnabled))
            errors.Add("At least one channel must be enabled");

        foreach (var channel in config.Channels)
        {
            ValidateChannelConfiguration(channel, errors);
        }

        // Validate Chrome configuration
        if (config.Chrome.PageLoadTimeout <= 0)
            errors.Add("Chrome PageLoadTimeout must be greater than 0");

        if (config.Chrome.ImplicitWaitTimeout <= 0)
            errors.Add("Chrome ImplicitWaitTimeout must be greater than 0");

        // Validate security configuration
        if (config.EnableDomainValidation && !config.AllowedDomains.Any())
            errors.Add("AllowedDomains cannot be empty when domain validation is enabled");

        if (config.Security.MaxUrlValidationRetries <= 0)
            errors.Add("Security MaxUrlValidationRetries must be greater than 0");

        if (config.Security.UrlValidationTimeout <= 0)
            errors.Add("Security UrlValidationTimeout must be greater than 0");

        if (errors.Any())
        {
            var errorMessage = "Configuration validation failed:\n" + string.Join("\n", errors);
            throw new InvalidOperationException(errorMessage);
        }

        _logger.Information("Configuration validation passed");
    }

    /// <summary>
    /// Validates a single channel configuration
    /// </summary>
    private void ValidateChannelConfiguration(ChannelConfiguration channel, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(channel.Name))
            errors.Add("Channel Name cannot be empty");

        if (channel.DelayBetweenActions < 0)
            errors.Add($"Channel {channel.Name}: DelayBetweenActions cannot be negative");

        if (channel.ScrollDelay < 0)
            errors.Add($"Channel {channel.Name}: ScrollDelay cannot be negative");

        if (channel.MaxRetries <= 0)
            errors.Add($"Channel {channel.Name}: MaxRetries must be greater than 0");

        // Check for duplicate channel names
        var duplicateNames = errors.Where(e => e.Contains($"Channel {channel.Name}:")).Count();
        if (duplicateNames > 1)
            errors.Add($"Duplicate channel name: {channel.Name}");
    }

    /// <summary>
    /// Ensures all required directories exist
    /// </summary>
    private void EnsureDirectoriesExist(AppConfiguration config)
    {
        var directories = new[]
        {
            config.SessionDataPath,
            config.LogsPath,
            config.InputFilesPath,
            Path.Combine(config.LogsPath, "channels")
        };

        foreach (var directory in directories)
        {
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.Information("Created directory: {Directory}", directory);
            }
        }

        // Create channel-specific profile directories
        foreach (var channel in config.Channels.Where(c => c.IsEnabled))
        {
            var profilePath = string.IsNullOrEmpty(channel.ProfilePath) 
                ? Path.Combine(config.SessionDataPath, "Profiles", channel.Name)
                : channel.ProfilePath;

            if (!Directory.Exists(profilePath))
            {
                Directory.CreateDirectory(profilePath);
                _logger.Information("Created channel profile directory: {Directory}", profilePath);
            }
        }
    }

    /// <summary>
    /// Gets the current configuration
    /// </summary>
    public AppConfiguration GetConfiguration()
    {
        return _configuration ?? throw new InvalidOperationException("Configuration not loaded");
    }

    /// <summary>
    /// Updates a specific channel configuration
    /// </summary>
    public void UpdateChannelConfiguration(string channelName, ChannelConfiguration newConfig)
    {
        if (_configuration == null)
            throw new InvalidOperationException("Configuration not loaded");

        var existingChannel = _configuration.Channels.FirstOrDefault(c => c.Name == channelName);
        if (existingChannel == null)
        {
            _configuration.Channels.Add(newConfig);
            _logger.Information("Added new channel configuration: {ChannelName}", channelName);
        }
        else
        {
            var index = _configuration.Channels.IndexOf(existingChannel);
            _configuration.Channels[index] = newConfig;
            _logger.Information("Updated channel configuration: {ChannelName}", channelName);
        }

        ValidateConfiguration(_configuration);
    }

    /// <summary>
    /// Removes a channel configuration
    /// </summary>
    public bool RemoveChannelConfiguration(string channelName)
    {
        if (_configuration == null)
            throw new InvalidOperationException("Configuration not loaded");

        var channel = _configuration.Channels.FirstOrDefault(c => c.Name == channelName);
        if (channel != null)
        {
            _configuration.Channels.Remove(channel);
            _logger.Information("Removed channel configuration: {ChannelName}", channelName);
            return true;
        }

        return false;
    }

    /// <summary>
    /// Gets configuration summary for logging
    /// </summary>
    public string GetConfigurationSummary()
    {
        if (_configuration == null)
            return "Configuration not loaded";

        return $"Channels: {_configuration.Channels.Count(c => c.IsEnabled)}/{_configuration.Channels.Count} enabled, " +
               $"Max Concurrent: {_configuration.MaxConcurrentChannels}, " +
               $"Headless: {_configuration.EnableHeadlessMode}, " +
               $"Domain Validation: {_configuration.EnableDomainValidation}";
    }
}
