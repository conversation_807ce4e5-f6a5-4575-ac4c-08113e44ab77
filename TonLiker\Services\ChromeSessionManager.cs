using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Serilog;
using TonLiker.Models;

namespace TonLiker.Services;

/// <summary>
/// Manages Chrome browser sessions with isolated profiles for each channel
/// </summary>
public class ChromeSessionManager
{
    private readonly AppConfiguration _config;
    private readonly ILogger _logger;
    private readonly Dictionary<string, ChannelSession> _activeSessions = new();

    public ChromeSessionManager(AppConfiguration config, ILogger logger)
    {
        _config = config;
        _logger = logger;
    }

    /// <summary>
    /// Creates a new Chrome session for the specified channel
    /// </summary>
    public async Task<ChannelSession> CreateSessionAsync(ChannelConfiguration channelConfig)
    {
        try
        {
            _logger.Information("Creating Chrome session for channel {ChannelName}", channelConfig.Name);

            var profilePath = GetChannelProfilePath(channelConfig);
            EnsureDirectoryExists(profilePath);

            var options = CreateChromeOptions(channelConfig, profilePath);
            var service = CreateChromeService();

            var driver = new ChromeDriver(service, options);
            ConfigureDriver(driver);

            var channelLogger = _logger.ForContext("Channel", channelConfig.Name);
            var session = new ChannelSession(channelConfig.Name, channelConfig, driver, channelLogger);

            _activeSessions[channelConfig.Name] = session;

            _logger.Information("Chrome session created successfully for channel {ChannelName}", channelConfig.Name);
            return session;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to create Chrome session for channel {ChannelName}", channelConfig.Name);
            throw;
        }
    }

    /// <summary>
    /// Gets the profile path for a specific channel
    /// </summary>
    private string GetChannelProfilePath(ChannelConfiguration channelConfig)
    {
        if (!string.IsNullOrEmpty(channelConfig.ProfilePath))
        {
            return channelConfig.ProfilePath;
        }

        return Path.Combine(_config.SessionDataPath, "Profiles", channelConfig.Name);
    }

    /// <summary>
    /// Creates Chrome options with proper isolation and security settings
    /// </summary>
    private ChromeOptions CreateChromeOptions(ChannelConfiguration channelConfig, string profilePath)
    {
        var options = new ChromeOptions();

        // Set user data directory for session isolation
        options.AddArgument($"--user-data-dir={profilePath}");
        options.AddArgument($"--profile-directory=Default");

        // Add default arguments from configuration
        foreach (var arg in _config.Chrome.DefaultArguments)
        {
            options.AddArgument(arg);
        }

        // Headless mode
        if (_config.EnableHeadlessMode)
        {
            options.AddArgument("--headless=new");
        }

        // Custom user agent if specified
        if (channelConfig.UserAgents.Any())
        {
            var userAgent = channelConfig.UserAgents[Random.Shared.Next(channelConfig.UserAgents.Count)];
            options.AddArgument($"--user-agent={userAgent}");
        }

        // Chrome binary path if specified
        if (!string.IsNullOrEmpty(_config.Chrome.ChromeBinaryPath))
        {
            options.BinaryLocation = _config.Chrome.ChromeBinaryPath;
        }

        // Additional security settings
        options.AddArgument("--disable-web-security");
        options.AddArgument("--disable-features=VizDisplayCompositor");
        options.AddArgument("--disable-ipc-flooding-protection");

        // Exclude automation switches
        options.AddExcludedArgument("enable-automation");
        options.AddAdditionalOption("useAutomationExtension", false);

        // Set preferences
        options.AddUserProfilePreference("credentials_enable_service", false);
        options.AddUserProfilePreference("profile.password_manager_enabled", false);

        return options;
    }

    /// <summary>
    /// Creates Chrome driver service
    /// </summary>
    private ChromeDriverService CreateChromeService()
    {
        ChromeDriverService service;

        if (!string.IsNullOrEmpty(_config.Chrome.ChromeDriverPath))
        {
            service = ChromeDriverService.CreateDefaultService(_config.Chrome.ChromeDriverPath);
        }
        else
        {
            service = ChromeDriverService.CreateDefaultService();
        }

        service.HideCommandPromptWindow = true;
        service.SuppressInitialDiagnosticInformation = true;

        return service;
    }

    /// <summary>
    /// Configures the WebDriver with timeouts and settings
    /// </summary>
    private void ConfigureDriver(IWebDriver driver)
    {
        driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(_config.Chrome.PageLoadTimeout);
        driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(_config.Chrome.ImplicitWaitTimeout);
        driver.Manage().Window.Maximize();
    }

    /// <summary>
    /// Ensures the specified directory exists
    /// </summary>
    private static void EnsureDirectoryExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }
    }

    /// <summary>
    /// Gets an active session by channel name
    /// </summary>
    public ChannelSession? GetSession(string channelName)
    {
        return _activeSessions.TryGetValue(channelName, out var session) ? session : null;
    }

    /// <summary>
    /// Closes a specific session
    /// </summary>
    public async Task CloseSessionAsync(string channelName)
    {
        if (_activeSessions.TryGetValue(channelName, out var session))
        {
            session.Dispose();
            _activeSessions.Remove(channelName);
            _logger.Information("Closed session for channel {ChannelName}", channelName);
        }
    }

    /// <summary>
    /// Closes all active sessions
    /// </summary>
    public async Task CloseAllSessionsAsync()
    {
        var tasks = _activeSessions.Keys.Select(CloseSessionAsync);
        await Task.WhenAll(tasks);
        _logger.Information("All Chrome sessions closed");
    }

    /// <summary>
    /// Gets the count of active sessions
    /// </summary>
    public int ActiveSessionCount => _activeSessions.Count;

    /// <summary>
    /// Gets all active session names
    /// </summary>
    public IEnumerable<string> ActiveSessionNames => _activeSessions.Keys;
}
