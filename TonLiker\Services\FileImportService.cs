using CsvHelper;
using CsvHelper.Configuration;
using Serilog;
using System.Globalization;
using TonLiker.Models;

namespace TonLiker.Services;

/// <summary>
/// Handles importing NFT URLs from CSV and TXT files
/// </summary>
public class FileImportService
{
    private readonly SecurityService _securityService;
    private readonly ILogger _logger;

    public FileImportService(SecurityService securityService, ILogger logger)
    {
        _securityService = securityService;
        _logger = logger;
    }

    /// <summary>
    /// Imports NFT URLs from a file (CSV or TXT)
    /// </summary>
    public async Task<ImportResult> ImportFromFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return ImportResult.Failed($"File not found: {filePath}");
            }

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            
            return extension switch
            {
                ".csv" => await ImportFromCsvAsync(filePath),
                ".txt" => await ImportFromTxtAsync(filePath),
                _ => ImportResult.Failed($"Unsupported file format: {extension}. Supported formats: .csv, .txt")
            };
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error importing from file {FilePath}", filePath);
            return ImportResult.Failed($"Import error: {ex.Message}");
        }
    }

    /// <summary>
    /// Imports NFT URLs from a CSV file
    /// </summary>
    private async Task<ImportResult> ImportFromCsvAsync(string filePath)
    {
        var result = new ImportResult();
        var nftItems = new List<NftItem>();

        try
        {
            using var reader = new StreamReader(filePath);
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                MissingFieldFound = null,
                HeaderValidated = null
            });

            // Try to read as structured CSV first
            var records = csv.GetRecords<NftCsvRecord>().ToList();
            
            foreach (var record in records)
            {
                if (string.IsNullOrWhiteSpace(record.Url))
                {
                    result.SkippedCount++;
                    continue;
                }

                var nftItem = new NftItem
                {
                    Url = record.Url.Trim(),
                    Title = record.Title?.Trim(),
                    Collection = record.Collection?.Trim()
                };

                var validationResult = await ValidateAndProcessNftItem(nftItem);
                
                if (validationResult.IsValid)
                {
                    nftItems.Add(nftItem);
                    result.ValidCount++;
                }
                else
                {
                    result.InvalidCount++;
                    result.ValidationErrors.Add($"URL: {nftItem.Url}, Error: {validationResult.ErrorMessage}");
                }

                result.TotalCount++;
            }
        }
        catch (Exception ex)
        {
            // If structured CSV reading fails, try reading as simple URL list
            _logger.Warning("Structured CSV reading failed, trying simple URL list: {Error}", ex.Message);
            return await ImportFromSimpleCsvAsync(filePath);
        }

        result.NftItems = nftItems;
        result.Success = true;

        _logger.Information("CSV import completed. Total: {Total}, Valid: {Valid}, Invalid: {Invalid}, Skipped: {Skipped}",
            result.TotalCount, result.ValidCount, result.InvalidCount, result.SkippedCount);

        return result;
    }

    /// <summary>
    /// Imports from CSV file treating it as simple URL list
    /// </summary>
    private async Task<ImportResult> ImportFromSimpleCsvAsync(string filePath)
    {
        var result = new ImportResult();
        var nftItems = new List<NftItem>();

        using var reader = new StreamReader(filePath);
        string? line;
        int lineNumber = 0;

        while ((line = await reader.ReadLineAsync()) != null)
        {
            lineNumber++;
            
            if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
            {
                result.SkippedCount++;
                continue;
            }

            // Split by comma and take first column as URL
            var parts = line.Split(',');
            var url = parts[0].Trim().Trim('"');

            if (string.IsNullOrWhiteSpace(url))
            {
                result.SkippedCount++;
                continue;
            }

            var nftItem = new NftItem { Url = url };
            
            // If there are more columns, try to extract title and collection
            if (parts.Length > 1 && !string.IsNullOrWhiteSpace(parts[1]))
            {
                nftItem.Title = parts[1].Trim().Trim('"');
            }
            
            if (parts.Length > 2 && !string.IsNullOrWhiteSpace(parts[2]))
            {
                nftItem.Collection = parts[2].Trim().Trim('"');
            }

            var validationResult = await ValidateAndProcessNftItem(nftItem);
            
            if (validationResult.IsValid)
            {
                nftItems.Add(nftItem);
                result.ValidCount++;
            }
            else
            {
                result.InvalidCount++;
                result.ValidationErrors.Add($"Line {lineNumber}: {validationResult.ErrorMessage}");
            }

            result.TotalCount++;
        }

        result.NftItems = nftItems;
        result.Success = true;

        return result;
    }

    /// <summary>
    /// Imports NFT URLs from a TXT file
    /// </summary>
    private async Task<ImportResult> ImportFromTxtAsync(string filePath)
    {
        var result = new ImportResult();
        var nftItems = new List<NftItem>();

        using var reader = new StreamReader(filePath);
        string? line;
        int lineNumber = 0;

        while ((line = await reader.ReadLineAsync()) != null)
        {
            lineNumber++;
            
            if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
            {
                result.SkippedCount++;
                continue;
            }

            var url = line.Trim();
            var nftItem = new NftItem { Url = url };

            var validationResult = await ValidateAndProcessNftItem(nftItem);
            
            if (validationResult.IsValid)
            {
                nftItems.Add(nftItem);
                result.ValidCount++;
            }
            else
            {
                result.InvalidCount++;
                result.ValidationErrors.Add($"Line {lineNumber}: {validationResult.ErrorMessage}");
            }

            result.TotalCount++;
        }

        result.NftItems = nftItems;
        result.Success = true;

        _logger.Information("TXT import completed. Total: {Total}, Valid: {Valid}, Invalid: {Invalid}, Skipped: {Skipped}",
            result.TotalCount, result.ValidCount, result.InvalidCount, result.SkippedCount);

        return result;
    }

    /// <summary>
    /// Validates and processes an NFT item
    /// </summary>
    private async Task<ValidationResult> ValidateAndProcessNftItem(NftItem nftItem)
    {
        try
        {
            // Basic URL validation
            var validationResult = await _securityService.ValidateUrlAsync(nftItem.Url);
            
            if (validationResult.IsValid)
            {
                nftItem.IsValidated = true;
                nftItem.Status = NftStatus.Validated;
            }
            else
            {
                nftItem.Status = NftStatus.Failed;
                nftItem.ErrorMessage = validationResult.ErrorMessage;
            }

            return validationResult;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error validating NFT item {Url}", nftItem.Url);
            nftItem.Status = NftStatus.Failed;
            nftItem.ErrorMessage = ex.Message;
            return ValidationResult.Invalid($"Validation error: {ex.Message}");
        }
    }

    /// <summary>
    /// Exports NFT items to CSV file
    /// </summary>
    public async Task<bool> ExportToCsvAsync(IEnumerable<NftItem> nftItems, string filePath)
    {
        try
        {
            using var writer = new StreamWriter(filePath);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

            await csv.WriteRecordsAsync(nftItems.Select(item => new NftExportRecord
            {
                Url = item.Url,
                Title = item.Title,
                Collection = item.Collection,
                Status = item.Status.ToString(),
                IsLiked = item.IsLiked,
                LastAttempt = item.LastAttempt?.ToString("yyyy-MM-dd HH:mm:ss"),
                AttemptCount = item.AttemptCount,
                ErrorMessage = item.ErrorMessage
            }));

            _logger.Information("Exported {Count} NFT items to {FilePath}", nftItems.Count(), filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error exporting to CSV file {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Gets all files in the input directory
    /// </summary>
    public IEnumerable<string> GetInputFiles(string inputDirectory)
    {
        try
        {
            if (!Directory.Exists(inputDirectory))
            {
                Directory.CreateDirectory(inputDirectory);
                return Enumerable.Empty<string>();
            }

            return Directory.GetFiles(inputDirectory, "*.*")
                .Where(file => file.EndsWith(".csv", StringComparison.OrdinalIgnoreCase) ||
                              file.EndsWith(".txt", StringComparison.OrdinalIgnoreCase))
                .OrderBy(file => file);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error getting input files from {Directory}", inputDirectory);
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// Validates file format and accessibility
    /// </summary>
    public FileValidationResult ValidateFile(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return FileValidationResult.Invalid("File does not exist");
            }

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            if (extension != ".csv" && extension != ".txt")
            {
                return FileValidationResult.Invalid("Unsupported file format. Only .csv and .txt files are supported");
            }

            var fileInfo = new FileInfo(filePath);
            if (fileInfo.Length == 0)
            {
                return FileValidationResult.Invalid("File is empty");
            }

            if (fileInfo.Length > 100 * 1024 * 1024) // 100MB limit
            {
                return FileValidationResult.Invalid("File is too large (max 100MB)");
            }

            // Try to read first few lines to validate format
            using var reader = new StreamReader(filePath);
            var firstLine = reader.ReadLine();
            
            if (string.IsNullOrWhiteSpace(firstLine))
            {
                return FileValidationResult.Invalid("File appears to be empty or contains only whitespace");
            }

            return FileValidationResult.Valid();
        }
        catch (Exception ex)
        {
            return FileValidationResult.Invalid($"File validation error: {ex.Message}");
        }
    }
}

/// <summary>
/// CSV record for importing NFT data
/// </summary>
public class NftCsvRecord
{
    public string Url { get; set; } = string.Empty;
    public string? Title { get; set; }
    public string? Collection { get; set; }
}

/// <summary>
/// CSV record for exporting NFT data
/// </summary>
public class NftExportRecord
{
    public string Url { get; set; } = string.Empty;
    public string? Title { get; set; }
    public string? Collection { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsLiked { get; set; }
    public string? LastAttempt { get; set; }
    public int AttemptCount { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Result of file import operation
/// </summary>
public class ImportResult
{
    public bool Success { get; set; } = false;
    public string ErrorMessage { get; set; } = string.Empty;
    public int TotalCount { get; set; } = 0;
    public int ValidCount { get; set; } = 0;
    public int InvalidCount { get; set; } = 0;
    public int SkippedCount { get; set; } = 0;
    public List<NftItem> NftItems { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();

    public static ImportResult Failed(string errorMessage) => new()
    {
        Success = false,
        ErrorMessage = errorMessage
    };
}

/// <summary>
/// Result of file validation
/// </summary>
public class FileValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }

    private FileValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }

    public static FileValidationResult Valid() => new(true);
    public static FileValidationResult Invalid(string errorMessage) => new(false, errorMessage);
}
