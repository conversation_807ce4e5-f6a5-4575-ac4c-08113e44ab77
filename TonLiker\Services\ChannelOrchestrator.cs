using Serilog;
using System.Collections.Concurrent;
using TonLiker.Models;

namespace TonLiker.Services;

/// <summary>
/// Orchestrates multiple browser channels for concurrent NFT processing
/// </summary>
public class ChannelOrchestrator
{
    private readonly AppConfiguration _config;
    private readonly ChromeSessionManager _sessionManager;
    private readonly NftAutomationService _automationService;
    private readonly FileImportService _fileImportService;
    private readonly ILogger _logger;
    
    private readonly ConcurrentDictionary<string, ChannelWorker> _activeWorkers = new();
    private readonly SemaphoreSlim _concurrencyLimiter;
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    public ChannelOrchestrator(
        AppConfiguration config,
        ChromeSessionManager sessionManager,
        NftAutomationService automationService,
        FileImportService fileImportService,
        ILogger logger)
    {
        _config = config;
        _sessionManager = sessionManager;
        _automationService = automationService;
        _fileImportService = fileImportService;
        _logger = logger;
        _concurrencyLimiter = new SemaphoreSlim(_config.MaxConcurrentChannels, _config.MaxConcurrentChannels);
    }

    /// <summary>
    /// Starts processing NFTs from a file across multiple channels
    /// </summary>
    public async Task<OrchestrationResult> ProcessFileAsync(string filePath)
    {
        try
        {
            _logger.Information("Starting file processing: {FilePath}", filePath);

            // Import NFTs from file
            var importResult = await _fileImportService.ImportFromFileAsync(filePath);
            if (!importResult.Success)
            {
                return OrchestrationResult.Failed($"File import failed: {importResult.ErrorMessage}");
            }

            if (!importResult.NftItems.Any())
            {
                return OrchestrationResult.Failed("No valid NFT URLs found in file");
            }

            _logger.Information("Imported {Count} valid NFT URLs", importResult.NftItems.Count);

            // Process NFTs across channels
            var result = await ProcessNftListAsync(importResult.NftItems);
            
            // Export results
            await ExportResultsAsync(importResult.NftItems, filePath);

            return result;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error processing file {FilePath}", filePath);
            return OrchestrationResult.Failed($"Processing error: {ex.Message}");
        }
    }

    /// <summary>
    /// Processes a list of NFT items across multiple channels
    /// </summary>
    public async Task<OrchestrationResult> ProcessNftListAsync(List<NftItem> nftItems)
    {
        var result = new OrchestrationResult();
        var nftQueue = new ConcurrentQueue<NftItem>(nftItems.Where(item => item.Status == NftStatus.Validated));

        try
        {
            _logger.Information("Starting processing of {Count} NFTs across {Channels} channels", 
                nftQueue.Count, _config.Channels.Count(c => c.IsEnabled));

            // Start channel workers
            var workerTasks = new List<Task>();
            
            foreach (var channelConfig in _config.Channels.Where(c => c.IsEnabled))
            {
                var workerTask = StartChannelWorkerAsync(channelConfig, nftQueue, result);
                workerTasks.Add(workerTask);
                
                // Delay between starting channels
                await Task.Delay(_config.GlobalDelayBetweenChannels);
            }

            // Wait for all workers to complete
            await Task.WhenAll(workerTasks);

            // Calculate final statistics
            result.TotalProcessed = nftItems.Count(item => item.Status != NftStatus.Pending);
            result.SuccessfulLikes = nftItems.Count(item => item.Status == NftStatus.Liked);
            result.FailedAttempts = nftItems.Count(item => item.Status == NftStatus.Failed);
            result.AlreadyLiked = nftItems.Count(item => item.Status == NftStatus.AlreadyLiked);
            result.Success = true;

            _logger.Information("Processing completed. Total: {Total}, Liked: {Liked}, Failed: {Failed}, Already Liked: {AlreadyLiked}",
                result.TotalProcessed, result.SuccessfulLikes, result.FailedAttempts, result.AlreadyLiked);

            return result;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in orchestration");
            return OrchestrationResult.Failed($"Orchestration error: {ex.Message}");
        }
        finally
        {
            await StopAllWorkersAsync();
        }
    }

    /// <summary>
    /// Processes a single page URL across multiple channels
    /// </summary>
    public async Task<OrchestrationResult> ProcessPageAsync(string pageUrl)
    {
        var result = new OrchestrationResult();

        try
        {
            _logger.Information("Starting page processing: {PageUrl}", pageUrl);

            var workerTasks = new List<Task>();

            foreach (var channelConfig in _config.Channels.Where(c => c.IsEnabled))
            {
                var workerTask = StartPageWorkerAsync(channelConfig, pageUrl, result);
                workerTasks.Add(workerTask);
                
                await Task.Delay(_config.GlobalDelayBetweenChannels);
            }

            await Task.WhenAll(workerTasks);

            result.Success = true;
            return result;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error processing page {PageUrl}", pageUrl);
            return OrchestrationResult.Failed($"Page processing error: {ex.Message}");
        }
        finally
        {
            await StopAllWorkersAsync();
        }
    }

    /// <summary>
    /// Starts a worker for processing NFTs from queue
    /// </summary>
    private async Task StartChannelWorkerAsync(ChannelConfiguration channelConfig, ConcurrentQueue<NftItem> nftQueue, OrchestrationResult result)
    {
        await _concurrencyLimiter.WaitAsync(_cancellationTokenSource.Token);
        
        try
        {
            var worker = new ChannelWorker(channelConfig, _sessionManager, _automationService, _logger);
            _activeWorkers[channelConfig.Name] = worker;

            await worker.ProcessNftQueueAsync(nftQueue, _cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in channel worker {ChannelName}", channelConfig.Name);
        }
        finally
        {
            _activeWorkers.TryRemove(channelConfig.Name, out _);
            _concurrencyLimiter.Release();
        }
    }

    /// <summary>
    /// Starts a worker for processing a single page
    /// </summary>
    private async Task StartPageWorkerAsync(ChannelConfiguration channelConfig, string pageUrl, OrchestrationResult result)
    {
        await _concurrencyLimiter.WaitAsync(_cancellationTokenSource.Token);
        
        try
        {
            var worker = new ChannelWorker(channelConfig, _sessionManager, _automationService, _logger);
            _activeWorkers[channelConfig.Name] = worker;

            var pageResult = await worker.ProcessPageAsync(pageUrl, _cancellationTokenSource.Token);
            
            lock (result)
            {
                result.TotalProcessed += pageResult.ProcessedCount;
                result.SuccessfulLikes += pageResult.LikedCount;
                result.FailedAttempts += pageResult.FailedCount;
                result.AlreadyLiked += pageResult.AlreadyLikedCount;
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in page worker {ChannelName}", channelConfig.Name);
        }
        finally
        {
            _activeWorkers.TryRemove(channelConfig.Name, out _);
            _concurrencyLimiter.Release();
        }
    }

    /// <summary>
    /// Exports processing results to file
    /// </summary>
    private async Task ExportResultsAsync(List<NftItem> nftItems, string originalFilePath)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var fileName = Path.GetFileNameWithoutExtension(originalFilePath);
            var exportPath = Path.Combine(_config.LogsPath, $"{fileName}_results_{timestamp}.csv");

            Directory.CreateDirectory(_config.LogsPath);
            
            await _fileImportService.ExportToCsvAsync(nftItems, exportPath);
            _logger.Information("Results exported to {ExportPath}", exportPath);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error exporting results");
        }
    }

    /// <summary>
    /// Stops all active workers
    /// </summary>
    public async Task StopAllWorkersAsync()
    {
        try
        {
            _cancellationTokenSource.Cancel();

            var stopTasks = _activeWorkers.Values.Select(worker => worker.StopAsync());
            await Task.WhenAll(stopTasks);

            _activeWorkers.Clear();
            
            await _sessionManager.CloseAllSessionsAsync();
            
            _logger.Information("All workers stopped");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error stopping workers");
        }
    }

    /// <summary>
    /// Gets the status of all active workers
    /// </summary>
    public WorkerStatus GetStatus()
    {
        return new WorkerStatus
        {
            ActiveWorkers = _activeWorkers.Count,
            WorkerDetails = _activeWorkers.Values.Select(w => new WorkerDetail
            {
                ChannelName = w.ChannelName,
                IsActive = w.IsActive,
                ProcessedCount = w.ProcessedCount,
                SuccessfulCount = w.SuccessfulCount,
                FailedCount = w.FailedCount
            }).ToList()
        };
    }

    public void Dispose()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        _concurrencyLimiter?.Dispose();
    }
}

/// <summary>
/// Result of orchestration operation
/// </summary>
public class OrchestrationResult
{
    public bool Success { get; set; } = false;
    public string ErrorMessage { get; set; } = string.Empty;
    public int TotalProcessed { get; set; } = 0;
    public int SuccessfulLikes { get; set; } = 0;
    public int FailedAttempts { get; set; } = 0;
    public int AlreadyLiked { get; set; } = 0;
    public TimeSpan Duration { get; set; }

    public static OrchestrationResult Failed(string errorMessage) => new()
    {
        Success = false,
        ErrorMessage = errorMessage
    };
}

/// <summary>
/// Status of all workers
/// </summary>
public class WorkerStatus
{
    public int ActiveWorkers { get; set; }
    public List<WorkerDetail> WorkerDetails { get; set; } = new();
}

/// <summary>
/// Details of a single worker
/// </summary>
public class WorkerDetail
{
    public string ChannelName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int ProcessedCount { get; set; }
    public int SuccessfulCount { get; set; }
    public int FailedCount { get; set; }
}
