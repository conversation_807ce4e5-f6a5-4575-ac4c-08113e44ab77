﻿using Serilog;
using TonLiker.Models;
using TonLiker.Services;

namespace TonLiker;

class Program
{
    private static ILogger? _logger;
    private static AppConfiguration? _config;
    private static ChannelOrchestrator? _orchestrator;

    static async Task<int> Main(string[] args)
    {
        try
        {
            // Initialize configuration
            var configService = new ConfigurationService(Log.Logger);
            _config = configService.LoadConfiguration();

            // Initialize logging
            _logger = LoggingService.ConfigureLogging(_config);
            Log.Logger = _logger;

            _logger.Information("TonLiker started. Configuration: {ConfigSummary}", configService.GetConfigurationSummary());

            // Initialize services
            var securityService = new SecurityService(_config, _logger);
            var sessionManager = new ChromeSessionManager(_config, _logger);
            var automationService = new NftAutomationService(_config, securityService, _logger);
            var fileImportService = new FileImportService(securityService, _logger);

            _orchestrator = new ChannelOrchestrator(_config, sessionManager, automationService, fileImportService, _logger);

            // Handle shutdown gracefully
            Console.CancelKeyPress += async (sender, e) =>
            {
                e.Cancel = true;
                _logger.Information("Shutdown requested...");
                await ShutdownAsync();
                Environment.Exit(0);
            };

            // Parse command line arguments and execute
            return await ExecuteCommandAsync(args);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Fatal error: {ex.Message}");
            _logger?.Error(ex, "Fatal error occurred");
            return 1;
        }
        finally
        {
            await ShutdownAsync();
            Log.CloseAndFlush();
        }
    }

    private static async Task<int> ExecuteCommandAsync(string[] args)
    {
        if (args.Length == 0)
        {
            ShowUsage();
            return 0;
        }

        var command = args[0].ToLowerInvariant();

        try
        {
            return command switch
            {
                "file" => await ProcessFileCommand(args),
                "page" => await ProcessPageCommand(args),
                "validate" => await ValidateCommand(args),
                "config" => await ConfigCommand(args),
                "help" => ShowUsage(),
                _ => ShowUsage()
            };
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error executing command {Command}", command);
            Console.WriteLine($"Error: {ex.Message}");
            return 1;
        }
    }

    private static async Task<int> ProcessFileCommand(string[] args)
    {
        if (args.Length < 2)
        {
            Console.WriteLine("Usage: tonliker file <file-path>");
            return 1;
        }

        var filePath = args[1];

        if (!File.Exists(filePath))
        {
            Console.WriteLine($"File not found: {filePath}");
            return 1;
        }

        _logger!.Information("Processing file: {FilePath}", filePath);
        Console.WriteLine($"Processing file: {filePath}");

        var result = await _orchestrator!.ProcessFileAsync(filePath);

        if (result.Success)
        {
            Console.WriteLine($"Processing completed successfully!");
            Console.WriteLine($"Total processed: {result.TotalProcessed}");
            Console.WriteLine($"Successfully liked: {result.SuccessfulLikes}");
            Console.WriteLine($"Failed attempts: {result.FailedAttempts}");
            Console.WriteLine($"Already liked: {result.AlreadyLiked}");
            return 0;
        }
        else
        {
            Console.WriteLine($"Processing failed: {result.ErrorMessage}");
            return 1;
        }
    }

    private static async Task<int> ProcessPageCommand(string[] args)
    {
        if (args.Length < 2)
        {
            Console.WriteLine("Usage: tonliker page <page-url>");
            return 1;
        }

        var pageUrl = args[1];

        if (!Uri.TryCreate(pageUrl, UriKind.Absolute, out _))
        {
            Console.WriteLine($"Invalid URL: {pageUrl}");
            return 1;
        }

        _logger!.Information("Processing page: {PageUrl}", pageUrl);
        Console.WriteLine($"Processing page: {pageUrl}");

        var result = await _orchestrator!.ProcessPageAsync(pageUrl);

        if (result.Success)
        {
            Console.WriteLine($"Page processing completed successfully!");
            Console.WriteLine($"Total processed: {result.TotalProcessed}");
            Console.WriteLine($"Successfully liked: {result.SuccessfulLikes}");
            Console.WriteLine($"Failed attempts: {result.FailedAttempts}");
            Console.WriteLine($"Already liked: {result.AlreadyLiked}");
            return 0;
        }
        else
        {
            Console.WriteLine($"Page processing failed: {result.ErrorMessage}");
            return 1;
        }
    }

    private static async Task<int> ValidateCommand(string[] args)
    {
        if (args.Length < 2)
        {
            Console.WriteLine("Usage: tonliker validate <file-path>");
            return 1;
        }

        var filePath = args[1];

        if (!File.Exists(filePath))
        {
            Console.WriteLine($"File not found: {filePath}");
            return 1;
        }

        var securityService = new SecurityService(_config!, _logger!);
        var fileImportService = new FileImportService(securityService, _logger!);

        Console.WriteLine($"Validating file: {filePath}");

        var fileValidation = fileImportService.ValidateFile(filePath);
        if (!fileValidation.IsValid)
        {
            Console.WriteLine($"File validation failed: {fileValidation.ErrorMessage}");
            return 1;
        }

        var importResult = await fileImportService.ImportFromFileAsync(filePath);

        Console.WriteLine($"Validation completed:");
        Console.WriteLine($"Total URLs: {importResult.TotalCount}");
        Console.WriteLine($"Valid URLs: {importResult.ValidCount}");
        Console.WriteLine($"Invalid URLs: {importResult.InvalidCount}");
        Console.WriteLine($"Skipped lines: {importResult.SkippedCount}");

        if (importResult.ValidationErrors.Any())
        {
            Console.WriteLine("\nValidation errors:");
            foreach (var error in importResult.ValidationErrors.Take(10))
            {
                Console.WriteLine($"  - {error}");
            }

            if (importResult.ValidationErrors.Count > 10)
            {
                Console.WriteLine($"  ... and {importResult.ValidationErrors.Count - 10} more errors");
            }
        }

        return 0;
    }

    private static async Task<int> ConfigCommand(string[] args)
    {
        if (args.Length < 2)
        {
            Console.WriteLine("Usage: tonliker config <show|create>");
            return 1;
        }

        var subCommand = args[1].ToLowerInvariant();

        switch (subCommand)
        {
            case "show":
                Console.WriteLine("Current configuration:");
                Console.WriteLine($"Session Data Path: {_config!.SessionDataPath}");
                Console.WriteLine($"Logs Path: {_config.LogsPath}");
                Console.WriteLine($"Input Files Path: {_config.InputFilesPath}");
                Console.WriteLine($"Max Concurrent Channels: {_config.MaxConcurrentChannels}");
                Console.WriteLine($"Headless Mode: {_config.EnableHeadlessMode}");
                Console.WriteLine($"Domain Validation: {_config.EnableDomainValidation}");
                Console.WriteLine($"Enabled Channels: {_config.Channels.Count(c => c.IsEnabled)}/{_config.Channels.Count}");
                break;

            case "create":
                var configService = new ConfigurationService(_logger!);
                await configService.SaveConfigurationAsync(_config!, "appsettings.example.json");
                Console.WriteLine("Example configuration created: appsettings.example.json");
                break;

            default:
                Console.WriteLine("Unknown config command. Use 'show' or 'create'.");
                return 1;
        }

        return 0;
    }

    private static int ShowUsage()
    {
        Console.WriteLine("TonLiker - NFT Automation Tool for getgems.io");
        Console.WriteLine();
        Console.WriteLine("Usage:");
        Console.WriteLine("  tonliker file <file-path>     Process NFTs from CSV/TXT file");
        Console.WriteLine("  tonliker page <page-url>      Process NFTs from a specific page");
        Console.WriteLine("  tonliker validate <file-path> Validate URLs in a file");
        Console.WriteLine("  tonliker config show          Show current configuration");
        Console.WriteLine("  tonliker config create        Create example configuration");
        Console.WriteLine("  tonliker help                 Show this help message");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  tonliker file ./InputFiles/nfts.csv");
        Console.WriteLine("  tonliker page https://getgems.io/collection/...");
        Console.WriteLine("  tonliker validate ./InputFiles/urls.txt");
        Console.WriteLine();
        Console.WriteLine("Configuration file: appsettings.json");
        Console.WriteLine("Logs directory: ./Logs");
        Console.WriteLine("Session data: ./SessionData");

        return 0;
    }

    private static async Task ShutdownAsync()
    {
        try
        {
            if (_orchestrator != null)
            {
                _logger?.Information("Shutting down orchestrator...");
                await _orchestrator.StopAllWorkersAsync();
                _orchestrator.Dispose();
            }

            _logger?.Information("TonLiker shutdown completed");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error during shutdown");
        }
    }
}
