using OpenQA.Selenium;
using Serilog;
using System.Text.RegularExpressions;
using TonLiker.Models;

namespace TonLiker.Services;

/// <summary>
/// Handles domain validation and security checks
/// </summary>
public class SecurityService
{
    private readonly AppConfiguration _config;
    private readonly ILogger _logger;
    private readonly HttpClient _httpClient;

    public SecurityService(AppConfiguration config, ILogger logger)
    {
        _config = config;
        _logger = logger;
        _httpClient = new HttpClient
        {
            Timeout = TimeSpan.FromSeconds(_config.Security.UrlValidationTimeout)
        };
    }

    /// <summary>
    /// Validates if a URL is safe and belongs to allowed domains
    /// </summary>
    public async Task<ValidationResult> ValidateUrlAsync(string url)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                return ValidationResult.Invalid("URL is empty or null");
            }

            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
            {
                return ValidationResult.Invalid("Invalid URL format");
            }

            // Check if domain is allowed
            if (!IsDomainAllowed(uri.Host))
            {
                return ValidationResult.Invalid($"Domain {uri.Host} is not in the allowed domains list");
            }

            // Check if domain is blocked
            if (IsDomainBlocked(uri.Host))
            {
                return ValidationResult.Invalid($"Domain {uri.Host} is in the blocked domains list");
            }

            // Check for suspicious patterns
            if (ContainsSuspiciousPatterns(url))
            {
                return ValidationResult.Invalid("URL contains suspicious patterns");
            }

            // Validate that it's an NFT URL
            if (!IsValidNftUrl(url))
            {
                return ValidationResult.Invalid("URL does not appear to be a valid NFT URL");
            }

            // Optional: Check if URL is accessible
            if (_config.Security.ValidateNftUrls)
            {
                var isAccessible = await IsUrlAccessibleAsync(url);
                if (!isAccessible)
                {
                    return ValidationResult.Invalid("URL is not accessible");
                }
            }

            return ValidationResult.Valid();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error validating URL {Url}", url);
            return ValidationResult.Invalid($"Validation error: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the current page domain in the browser
    /// </summary>
    public ValidationResult ValidateCurrentDomain(IWebDriver driver)
    {
        try
        {
            var currentUrl = driver.Url;
            if (string.IsNullOrEmpty(currentUrl))
            {
                return ValidationResult.Invalid("Current URL is empty");
            }

            if (!Uri.TryCreate(currentUrl, UriKind.Absolute, out var uri))
            {
                return ValidationResult.Invalid("Current URL has invalid format");
            }

            if (!IsDomainAllowed(uri.Host))
            {
                _logger.Warning("Browser navigated to unauthorized domain: {Domain}", uri.Host);
                return ValidationResult.Invalid($"Current domain {uri.Host} is not authorized");
            }

            return ValidationResult.Valid();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error validating current domain");
            return ValidationResult.Invalid($"Domain validation error: {ex.Message}");
        }
    }

    /// <summary>
    /// Checks if a domain is in the allowed list
    /// </summary>
    private bool IsDomainAllowed(string domain)
    {
        if (!_config.EnableDomainValidation)
            return true;

        return _config.AllowedDomains.Any(allowed => 
            domain.Equals(allowed, StringComparison.OrdinalIgnoreCase) ||
            domain.EndsWith($".{allowed}", StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if a domain is in the blocked list
    /// </summary>
    private bool IsDomainBlocked(string domain)
    {
        return _config.Security.BlockedDomains.Any(blocked =>
            domain.Equals(blocked, StringComparison.OrdinalIgnoreCase) ||
            domain.EndsWith($".{blocked}", StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if URL contains suspicious patterns
    /// </summary>
    private bool ContainsSuspiciousPatterns(string url)
    {
        var lowerUrl = url.ToLowerInvariant();
        return _config.Security.SuspiciousPatterns.Any(pattern =>
            lowerUrl.Contains(pattern.ToLowerInvariant()));
    }

    /// <summary>
    /// Validates if URL appears to be a valid NFT URL on getgems.io
    /// </summary>
    private bool IsValidNftUrl(string url)
    {
        // Basic pattern matching for getgems.io NFT URLs
        var nftPatterns = new[]
        {
            @"getgems\.io/nft/",
            @"getgems\.io/collection/.+/nft/",
            @"getgems\.io/collection/.+/item/"
        };

        return nftPatterns.Any(pattern => Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase));
    }

    /// <summary>
    /// Checks if URL is accessible via HTTP request
    /// </summary>
    private async Task<bool> IsUrlAccessibleAsync(string url)
    {
        for (int attempt = 0; attempt < _config.Security.MaxUrlValidationRetries; attempt++)
        {
            try
            {
                using var response = await _httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.Debug("URL accessibility check failed (attempt {Attempt}): {Error}", attempt + 1, ex.Message);
                
                if (attempt < _config.Security.MaxUrlValidationRetries - 1)
                {
                    await Task.Delay(1000 * (attempt + 1)); // Progressive delay
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Monitors for potential security threats during automation
    /// </summary>
    public SecurityThreat? DetectSecurityThreats(IWebDriver driver)
    {
        try
        {
            var currentUrl = driver.Url;
            var pageTitle = driver.Title;

            // Check for domain changes
            if (!Uri.TryCreate(currentUrl, UriKind.Absolute, out var uri) || !IsDomainAllowed(uri.Host))
            {
                return new SecurityThreat
                {
                    Type = ThreatType.UnauthorizedDomain,
                    Description = $"Browser navigated to unauthorized domain: {uri?.Host}",
                    Url = currentUrl,
                    Severity = ThreatSeverity.High
                };
            }

            // Check for suspicious page titles
            if (ContainsSuspiciousPatterns(pageTitle))
            {
                return new SecurityThreat
                {
                    Type = ThreatType.SuspiciousContent,
                    Description = $"Page title contains suspicious patterns: {pageTitle}",
                    Url = currentUrl,
                    Severity = ThreatSeverity.Medium
                };
            }

            // Check for wallet connection prompts (basic detection)
            try
            {
                var walletElements = driver.FindElements(By.XPath("//*[contains(text(), 'Connect Wallet') or contains(text(), 'Sign Transaction') or contains(text(), 'Approve')]"));
                if (walletElements.Any())
                {
                    return new SecurityThreat
                    {
                        Type = ThreatType.WalletInteraction,
                        Description = "Wallet interaction detected",
                        Url = currentUrl,
                        Severity = ThreatSeverity.Medium
                    };
                }
            }
            catch (NoSuchElementException)
            {
                // No wallet elements found, which is expected
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error detecting security threats");
            return null;
        }
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}

/// <summary>
/// Result of URL validation
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }

    private ValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }

    public static ValidationResult Valid() => new(true);
    public static ValidationResult Invalid(string errorMessage) => new(false, errorMessage);
}

/// <summary>
/// Represents a security threat
/// </summary>
public class SecurityThreat
{
    public ThreatType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public ThreatSeverity Severity { get; set; }
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
}

public enum ThreatType
{
    UnauthorizedDomain,
    SuspiciousContent,
    WalletInteraction,
    PhishingAttempt,
    MaliciousScript
}

public enum ThreatSeverity
{
    Low,
    Medium,
    High,
    Critical
}
