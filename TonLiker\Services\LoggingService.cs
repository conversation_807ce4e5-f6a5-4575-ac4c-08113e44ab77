using Serilog;
using Serilog.Events;
using TonLiker.Models;

namespace TonLiker.Services;

/// <summary>
/// Configures and manages logging for the application
/// </summary>
public static class LoggingService
{
    /// <summary>
    /// Configures Serilog with appropriate sinks and formatting
    /// </summary>
    public static ILogger ConfigureLogging(AppConfiguration config)
    {
        // Ensure logs directory exists
        Directory.CreateDirectory(config.LogsPath);

        var loggerConfig = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("Application", "TonLiker")
            .Enrich.WithProperty("Version", GetApplicationVersion());

        // Console sink
        loggerConfig.WriteTo.Console(
            outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}");

        // Main application log file
        loggerConfig.WriteTo.File(
            path: Path.Combine(config.LogsPath, "tonliker-.log"),
            rollingInterval: RollingInterval.Day,
            retainedFileCountLimit: 30,
            outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}");

        // Error-only log file
        loggerConfig.WriteTo.File(
            path: Path.Combine(config.LogsPath, "errors-.log"),
            restrictedToMinimumLevel: LogEventLevel.Error,
            rollingInterval: RollingInterval.Day,
            retainedFileCountLimit: 90,
            outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}");

        // Channel-specific log files
        loggerConfig.WriteTo.Logger(channelLogger =>
        {
            channelLogger
                .Filter.ByIncludingOnly(evt => evt.Properties.ContainsKey("Channel"))
                .WriteTo.File(
                    path: Path.Combine(config.LogsPath, "channels", "channel-{Channel}-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}");
        });

        // Performance log for metrics
        loggerConfig.WriteTo.Logger(perfLogger =>
        {
            perfLogger
                .Filter.ByIncludingOnly(evt => evt.Properties.ContainsKey("Performance"))
                .WriteTo.File(
                    path: Path.Combine(config.LogsPath, "performance-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] {Message:lj}{NewLine}");
        });

        return loggerConfig.CreateLogger();
    }

    /// <summary>
    /// Creates a channel-specific logger
    /// </summary>
    public static ILogger CreateChannelLogger(ILogger baseLogger, string channelName)
    {
        return baseLogger.ForContext("Channel", channelName);
    }

    /// <summary>
    /// Creates a performance logger for metrics
    /// </summary>
    public static ILogger CreatePerformanceLogger(ILogger baseLogger)
    {
        return baseLogger.ForContext("Performance", true);
    }

    /// <summary>
    /// Gets the application version
    /// </summary>
    private static string GetApplicationVersion()
    {
        try
        {
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            return version?.ToString() ?? "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }
}

/// <summary>
/// Handles error reporting and recovery
/// </summary>
public class ErrorHandler
{
    private readonly ILogger _logger;
    private readonly Dictionary<string, int> _errorCounts = new();
    private readonly Dictionary<string, DateTime> _lastErrorTimes = new();

    public ErrorHandler(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Handles and logs an error with context
    /// </summary>
    public void HandleError(Exception exception, string context, string? channelName = null, Dictionary<string, object>? additionalData = null)
    {
        var errorKey = $"{context}:{exception.GetType().Name}";
        
        // Track error frequency
        _errorCounts[errorKey] = _errorCounts.GetValueOrDefault(errorKey, 0) + 1;
        _lastErrorTimes[errorKey] = DateTime.UtcNow;

        // Create enriched logger
        var contextLogger = _logger;
        
        if (!string.IsNullOrEmpty(channelName))
        {
            contextLogger = contextLogger.ForContext("Channel", channelName);
        }

        contextLogger = contextLogger
            .ForContext("ErrorContext", context)
            .ForContext("ErrorCount", _errorCounts[errorKey])
            .ForContext("ErrorType", exception.GetType().Name);

        // Add additional data if provided
        if (additionalData != null)
        {
            foreach (var kvp in additionalData)
            {
                contextLogger = contextLogger.ForContext(kvp.Key, kvp.Value);
            }
        }

        // Log with appropriate level based on frequency
        if (_errorCounts[errorKey] == 1)
        {
            contextLogger.Error(exception, "Error in {Context}: {Message}", context, exception.Message);
        }
        else if (_errorCounts[errorKey] <= 5)
        {
            contextLogger.Warning(exception, "Recurring error in {Context} (count: {Count}): {Message}", 
                context, _errorCounts[errorKey], exception.Message);
        }
        else
        {
            // Reduce noise for frequently occurring errors
            contextLogger.Debug(exception, "Frequent error in {Context} (count: {Count}): {Message}", 
                context, _errorCounts[errorKey], exception.Message);
        }
    }

    /// <summary>
    /// Determines if an error should trigger a retry
    /// </summary>
    public bool ShouldRetry(Exception exception, int currentAttempt, int maxAttempts)
    {
        if (currentAttempt >= maxAttempts)
            return false;

        // Don't retry for certain types of exceptions
        var nonRetryableExceptions = new[]
        {
            typeof(ArgumentException),
            typeof(ArgumentNullException),
            typeof(InvalidOperationException),
            typeof(UnauthorizedAccessException)
        };

        if (nonRetryableExceptions.Contains(exception.GetType()))
            return false;

        // Don't retry if error is too frequent
        var errorKey = exception.GetType().Name;
        if (_errorCounts.GetValueOrDefault(errorKey, 0) > 10)
        {
            var lastError = _lastErrorTimes.GetValueOrDefault(errorKey, DateTime.MinValue);
            if (DateTime.UtcNow - lastError < TimeSpan.FromMinutes(5))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// Gets error statistics
    /// </summary>
    public ErrorStatistics GetErrorStatistics()
    {
        return new ErrorStatistics
        {
            TotalErrors = _errorCounts.Values.Sum(),
            UniqueErrors = _errorCounts.Count,
            MostFrequentErrors = _errorCounts
                .OrderByDescending(kvp => kvp.Value)
                .Take(10)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
            RecentErrors = _lastErrorTimes
                .Where(kvp => DateTime.UtcNow - kvp.Value < TimeSpan.FromHours(1))
                .Count()
        };
    }

    /// <summary>
    /// Resets error tracking
    /// </summary>
    public void Reset()
    {
        _errorCounts.Clear();
        _lastErrorTimes.Clear();
        _logger.Information("Error tracking reset");
    }
}

/// <summary>
/// Performance metrics tracker
/// </summary>
public class PerformanceTracker
{
    private readonly ILogger _performanceLogger;
    private readonly Dictionary<string, List<TimeSpan>> _operationTimes = new();

    public PerformanceTracker(ILogger logger)
    {
        _performanceLogger = LoggingService.CreatePerformanceLogger(logger);
    }

    /// <summary>
    /// Tracks the duration of an operation
    /// </summary>
    public void TrackOperation(string operationName, TimeSpan duration, string? channelName = null)
    {
        if (!_operationTimes.ContainsKey(operationName))
        {
            _operationTimes[operationName] = new List<TimeSpan>();
        }

        _operationTimes[operationName].Add(duration);

        var contextLogger = _performanceLogger;
        if (!string.IsNullOrEmpty(channelName))
        {
            contextLogger = contextLogger.ForContext("Channel", channelName);
        }

        contextLogger.Information("Operation {OperationName} completed in {Duration}ms", 
            operationName, duration.TotalMilliseconds);
    }

    /// <summary>
    /// Gets performance statistics for an operation
    /// </summary>
    public OperationStatistics? GetOperationStatistics(string operationName)
    {
        if (!_operationTimes.ContainsKey(operationName) || !_operationTimes[operationName].Any())
            return null;

        var times = _operationTimes[operationName];
        var sortedTimes = times.OrderBy(t => t).ToList();

        return new OperationStatistics
        {
            OperationName = operationName,
            Count = times.Count,
            AverageDuration = TimeSpan.FromTicks((long)times.Average(t => t.Ticks)),
            MinDuration = sortedTimes.First(),
            MaxDuration = sortedTimes.Last(),
            MedianDuration = sortedTimes[sortedTimes.Count / 2],
            TotalDuration = TimeSpan.FromTicks(times.Sum(t => t.Ticks))
        };
    }

    /// <summary>
    /// Gets all performance statistics
    /// </summary>
    public Dictionary<string, OperationStatistics> GetAllStatistics()
    {
        return _operationTimes.Keys
            .Select(GetOperationStatistics)
            .Where(stats => stats != null)
            .ToDictionary(stats => stats!.OperationName, stats => stats!);
    }
}

/// <summary>
/// Error statistics
/// </summary>
public class ErrorStatistics
{
    public int TotalErrors { get; set; }
    public int UniqueErrors { get; set; }
    public Dictionary<string, int> MostFrequentErrors { get; set; } = new();
    public int RecentErrors { get; set; }
}

/// <summary>
/// Operation performance statistics
/// </summary>
public class OperationStatistics
{
    public string OperationName { get; set; } = string.Empty;
    public int Count { get; set; }
    public TimeSpan AverageDuration { get; set; }
    public TimeSpan MinDuration { get; set; }
    public TimeSpan MaxDuration { get; set; }
    public TimeSpan MedianDuration { get; set; }
    public TimeSpan TotalDuration { get; set; }
}
