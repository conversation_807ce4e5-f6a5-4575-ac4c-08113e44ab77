{"format": 1, "restore": {"E:\\Projects\\MOS\\DONE\\FullSolution\\TonLiker\\TonLiker\\TonLiker.csproj": {}}, "projects": {"E:\\Projects\\MOS\\DONE\\FullSolution\\TonLiker\\TonLiker\\TonLiker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\MOS\\DONE\\FullSolution\\TonLiker\\TonLiker\\TonLiker.csproj", "projectName": "TonL<PERSON>r", "projectPath": "E:\\Projects\\MOS\\DONE\\FullSolution\\TonLiker\\TonLiker\\TonLiker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\MOS\\DONE\\FullSolution\\TonLiker\\TonLiker\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"CsvHelper": {"target": "Package", "version": "[33.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.26.1, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[131.0.6778.8500, )"}, "Serilog": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}