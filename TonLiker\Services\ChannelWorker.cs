using Serilog;
using System.Collections.Concurrent;
using TonLiker.Models;

namespace TonLiker.Services;

/// <summary>
/// Individual worker that processes NFTs on a single channel
/// </summary>
public class ChannelWorker
{
    private readonly ChannelConfiguration _channelConfig;
    private readonly ChromeSessionManager _sessionManager;
    private readonly NftAutomationService _automationService;
    private readonly ILogger _logger;
    
    private ChannelSession? _session;
    private bool _isActive = false;

    public string ChannelName => _channelConfig.Name;
    public bool IsActive => _isActive;
    public int ProcessedCount { get; private set; } = 0;
    public int SuccessfulCount { get; private set; } = 0;
    public int FailedCount { get; private set; } = 0;

    public ChannelWorker(
        ChannelConfiguration channelConfig,
        ChromeSessionManager sessionManager,
        NftAutomationService automationService,
        ILogger logger)
    {
        _channelConfig = channelConfig;
        _sessionManager = sessionManager;
        _automationService = automationService;
        _logger = logger.ForContext("Channel", channelConfig.Name);
    }

    /// <summary>
    /// Processes NFTs from a queue
    /// </summary>
    public async Task ProcessNftQueueAsync(ConcurrentQueue<NftItem> nftQueue, CancellationToken cancellationToken)
    {
        try
        {
            _logger.Information("Starting NFT queue processing for channel {ChannelName}", _channelConfig.Name);
            
            _session = await _sessionManager.CreateSessionAsync(_channelConfig);
            _isActive = true;

            while (!cancellationToken.IsCancellationRequested && nftQueue.TryDequeue(out var nftItem))
            {
                try
                {
                    var result = await ProcessSingleNftAsync(nftItem, cancellationToken);
                    
                    ProcessedCount++;
                    
                    if (result.Success)
                    {
                        SuccessfulCount++;
                    }
                    else
                    {
                        FailedCount++;
                    }

                    // Delay between NFTs
                    await Task.Delay(_channelConfig.DelayBetweenActions, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.Information("NFT processing cancelled for channel {ChannelName}", _channelConfig.Name);
                    break;
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error processing NFT {Url} on channel {ChannelName}", nftItem.Url, _channelConfig.Name);
                    FailedCount++;
                }
            }

            _logger.Information("NFT queue processing completed for channel {ChannelName}. Processed: {Processed}, Successful: {Successful}, Failed: {Failed}",
                _channelConfig.Name, ProcessedCount, SuccessfulCount, FailedCount);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in NFT queue processing for channel {ChannelName}", _channelConfig.Name);
        }
        finally
        {
            await StopAsync();
        }
    }

    /// <summary>
    /// Processes a single page
    /// </summary>
    public async Task<PageProcessingResult> ProcessPageAsync(string pageUrl, CancellationToken cancellationToken)
    {
        try
        {
            _logger.Information("Starting page processing for channel {ChannelName}: {PageUrl}", _channelConfig.Name, pageUrl);
            
            _session = await _sessionManager.CreateSessionAsync(_channelConfig);
            _isActive = true;

            var result = await _automationService.ProcessPageAsync(_session, pageUrl);
            
            ProcessedCount = result.ProcessedCount;
            SuccessfulCount = result.LikedCount;
            FailedCount = result.FailedCount;

            _logger.Information("Page processing completed for channel {ChannelName}. Processed: {Processed}, Liked: {Liked}, Failed: {Failed}",
                _channelConfig.Name, result.ProcessedCount, result.LikedCount, result.FailedCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in page processing for channel {ChannelName}", _channelConfig.Name);
            return PageProcessingResult.Failed($"Page processing error: {ex.Message}");
        }
        finally
        {
            await StopAsync();
        }
    }

    /// <summary>
    /// Processes a single NFT with retry logic
    /// </summary>
    private async Task<NftProcessingResult> ProcessSingleNftAsync(NftItem nftItem, CancellationToken cancellationToken)
    {
        if (_session == null)
        {
            return NftProcessingResult.Failed("No active session");
        }

        var maxRetries = _channelConfig.MaxRetries;
        var attempt = 0;

        while (attempt < maxRetries && !cancellationToken.IsCancellationRequested)
        {
            try
            {
                attempt++;
                _logger.Debug("Processing NFT {Url} (attempt {Attempt}/{MaxRetries}) on channel {ChannelName}", 
                    nftItem.Url, attempt, maxRetries, _channelConfig.Name);

                var result = await _automationService.ProcessNftAsync(_session, nftItem);
                
                if (result.Success)
                {
                    _logger.Information("Successfully processed NFT {Url} on channel {ChannelName}", nftItem.Url, _channelConfig.Name);
                    return result;
                }

                if (attempt < maxRetries)
                {
                    _logger.Warning("NFT processing failed (attempt {Attempt}/{MaxRetries}): {Error}. Retrying...", 
                        attempt, maxRetries, result.Message);
                    
                    // Progressive delay for retries
                    var retryDelay = _channelConfig.DelayBetweenActions * attempt;
                    await Task.Delay(retryDelay, cancellationToken);
                }
                else
                {
                    _logger.Error("NFT processing failed after {MaxRetries} attempts: {Error}", maxRetries, result.Message);
                    return result;
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Exception during NFT processing (attempt {Attempt}/{MaxRetries}) on channel {ChannelName}", 
                    attempt, maxRetries, _channelConfig.Name);
                
                if (attempt >= maxRetries)
                {
                    return NftProcessingResult.Failed($"Exception after {maxRetries} attempts: {ex.Message}");
                }
            }
        }

        return NftProcessingResult.Failed("Processing cancelled or max retries exceeded");
    }

    /// <summary>
    /// Stops the worker and cleans up resources
    /// </summary>
    public async Task StopAsync()
    {
        try
        {
            _isActive = false;
            
            if (_session != null)
            {
                await _sessionManager.CloseSessionAsync(_session.ChannelName);
                _session = null;
            }

            _logger.Information("Channel worker {ChannelName} stopped", _channelConfig.Name);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error stopping channel worker {ChannelName}", _channelConfig.Name);
        }
    }
}
